#pragma once

#include <Eigen/Dense>
#include <vector>
#include <stdexcept>

namespace CubicPolynomial {
    /**
     * Represents a cubic polynomial f(s) = as³ + bs² + cs + d
     * Coefficients are stored as [a, b, c, d]
     */
    using Coefficients = Eigen::Vector4d;

    /**
     * Represents a 2D point
     */
    using Point2D = Eigen::Vector2d;

    /**
     * Represents a heading (direction vector)
     */
    using Heading2D = Eigen::Vector2d;

    /**
     * Evaluate cubic polynomial at given s
     * @param coeffs Polynomial coefficients [a, b, c, d]
     * @param s Point to evaluate at
     * @return f(s) = as³ + bs² + cs + d
     */
    double evaluate(const Coefficients &coeffs, double s);

    /**
     * Evaluate first derivative of cubic polynomial at given s
     * @param coeffs Polynomial coefficients [a, b, c, d]
     * @param x Point to evaluate at
     * @return f'(s) = 3as² + 2bs + c
     */
    double evaluateDerivative(const Coefficients &coeffs, double s);

    /**
     * Evaluate second derivative of cubic polynomial at given s
     * @param coeffs Polynomial coefficients [a, b, c, d]
     * @param s Point to evaluate at
     * @return f''(s) = 6as + 2b
     */
    double evaluateSecondDerivative(const Coefficients &coeffs, double s);

    /**
     * Fit cubic polynomial through 4 points
     * @param points1D Vector of 4 points [x0, x1, x2, x3]
     * @param s_values Vector of 4 values [s0, s1, s2, s3]
     * @return Polynomial coefficients [a, b, c, d]
     * @throws std::invalid_argument if not exactly 4 points provided
     */
    Coefficients fitFourPoints1D(const std::vector<double> &points1D, std::vector<double> &s_values);

    /**
     * Fit cubic polynomial given two points and their headings
     * This creates a Hermite interpolation where:
     * - f(s0) = x0, f'(s0) = m0 (slope at s0)
     * - f(s1) = x1, f'(s1) = m1 (slope at s1)
     * @param point0 First point x0
     * @param slope0 Slope at first point
     * @param point1 Second point x1
     * @param slope1 Slope at second point
     * @param s0 s_value at first point
     * @param s1 s_value at second point
     * @return Polynomial coefficients [a, b, c, d]
     * @throws std::invalid_argument if headings have zero x-component
     */
    Coefficients fitPointSlopePointSlope1D(
        const double &point0, const double &slope0,
        const double &point1, const double &slope1,
        const double &s0 = 0.0, const double &s1 = 1.0
    );

    /**
     * Get polynomial coefficients as a string representation
     * @param coeffs Polynomial coefficients [a, b, c, d]
     * @return String like "2.5s³ + 1.2s² - 0.8s + 3.1"
     */
    std::string toString(const Coefficients &coeffs);

    /**
     * Evaluate polynomial at multiple s values
     * @param coeffs Polynomial coefficients [a, b, c, d]
     * @param s_values Vector of s values to evaluate at
     * @return Vector of corresponding x values
     */
    std::vector<double> evaluateMultiple(const Coefficients &coeffs, const std::vector<double> &s_values);
} // namespace CubicPolynomial
