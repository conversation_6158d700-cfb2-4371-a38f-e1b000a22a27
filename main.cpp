#include <iostream>
#include <iomanip>
#include "cubic_polynomial.h"

using namespace CubicPolynomial;

void demonstrateBasicEvaluation() {
    std::cout << "=== Basic Polynomial Evaluation ===" << std::endl;

    // Create polynomial f(x) = 2x³ - 3x² + x + 5
    Coefficients coeffs;
    coeffs << 2.0, -3.0, 1.0, 5.0;

    std::cout << "Polynomial: " << toString(coeffs) << std::endl;

    // Evaluate at several points
    std::vector<double> s_values = {-2.0, -1.0, 0.0, 1.0, 2.0, 3.0};

    std::cout << std::fixed << std::setprecision(3);
    std::cout << "\nEvaluation table:" << std::endl;
    std::cout << "x\tf(x)\tf'(x)\tf''(x)" << std::endl;
    std::cout << "---\t----\t-----\t------" << std::endl;

    for (double s : s_values) {
        double f = evaluate(coeffs, s);
        double fp = evaluateDerivative(coeffs, s);
        double fpp = evaluateSecondDerivative(coeffs, s);
        std::cout << s << "\t" << f << "\t" << fp << "\t" << fpp << std::endl;
    }
    std::cout << std::endl;
}

void demonstrateFourPointFitting() {
    std::cout << "=== Four Point Fitting ===" << std::endl;

    // Create 4 points that should fit a known polynomial
    // Let's use f(x) = x³ - 2x² + 3x - 1
    std::vector<Point2D> points = {
        Point2D(0.0, -1.0),   // f(0) = -1
        Point2D(1.0, 1.0),    // f(1) = 1 - 2 + 3 - 1 = 1
        Point2D(2.0, 5.0),    // f(2) = 8 - 8 + 6 - 1 = 5
        Point2D(3.0, 17.0)    // f(3) = 27 - 18 + 9 - 1 = 17
    };

    std::cout << "Input points:" << std::endl;
    for (size_t i = 0; i < points.size(); ++i) {
        std::cout << "  Point " << i+1 << ": (" << points[i][0] << ", " << points[i][1] << ")" << std::endl;
    }

    Coefficients fitted = fitFourPoints(points);
    std::cout << "\nFitted polynomial: " << toString(fitted) << std::endl;

    // Verify the fit
    std::cout << "\nVerification (should match input points):" << std::endl;
    for (const auto& point : points) {
        double x = point[0];
        double y_expected = point[1];
        double y_fitted = evaluate(fitted, x);
        std::cout << "  x=" << x << ": expected=" << y_expected << ", fitted=" << y_fitted
                  << ", error=" << std::abs(y_expected - y_fitted) << std::endl;
    }
    std::cout << std::endl;
}

void demonstrateHermiteInterpolation() {
    std::cout << "=== Point-Heading-Point-Heading Fitting (Hermite Interpolation) ===" << std::endl;

    // Define two points with their tangent directions
    Point2D p1(0.0, 1.0);
    Heading2D h1(1.0, 2.0);  // slope = 2 at x=0
    Point2D p2(2.0, 5.0);
    Heading2D h2(1.0, -1.0); // slope = -1 at x=2

    std::cout << "Point 1: (" << p1[0] << ", " << p1[1] << ") with heading ("
              << h1[0] << ", " << h1[1] << "), slope = " << h1[1]/h1[0] << std::endl;
    std::cout << "Point 2: (" << p2[0] << ", " << p2[1] << ") with heading ("
              << h2[0] << ", " << h2[1] << "), slope = " << h2[1]/h2[0] << std::endl;

    Coefficients fitted = fitPointHeadingPointHeading(p1, h1, p2, h2);
    std::cout << "\nFitted polynomial: " << toString(fitted) << std::endl;

    // Verify the constraints
    std::cout << "\nVerification:" << std::endl;
    std::cout << "  f(" << p1[0] << ") = " << evaluate(fitted, p1[0])
              << " (should be " << p1[1] << ")" << std::endl;
    std::cout << "  f(" << p2[0] << ") = " << evaluate(fitted, p2[0])
              << " (should be " << p2[1] << ")" << std::endl;
    std::cout << "  f'(" << p1[0] << ") = " << evaluateDerivative(fitted, p1[0])
              << " (should be " << h1[1]/h1[0] << ")" << std::endl;
    std::cout << "  f'(" << p2[0] << ") = " << evaluateDerivative(fitted, p2[0])
              << " (should be " << h2[1]/h2[0] << ")" << std::endl;

    // Show the curve between the points
    std::cout << "\nCurve evaluation between points:" << std::endl;
    std::cout << "x\tf(x)\tf'(x)" << std::endl;
    std::cout << "---\t----\t-----" << std::endl;
    for (double x = p1[0]; x <= p2[0]; x += 0.5) {
        double f = evaluate(fitted, x);
        double fp = evaluateDerivative(fitted, x);
        std::cout << x << "\t" << f << "\t" << fp << std::endl;
    }
    std::cout << std::endl;
}

int main() {
    std::cout << "Cubic Polynomial Library Demonstration" << std::endl;
    std::cout << "======================================" << std::endl << std::endl;

    try {
        demonstrateBasicEvaluation();
        demonstrateFourPointFitting();
        demonstrateHermiteInterpolation();

        std::cout << "All demonstrations completed successfully!" << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}